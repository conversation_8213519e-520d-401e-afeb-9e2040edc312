import { ElCascaderInstance } from '../ele-app/el';
import { CascaderOption } from './types';

declare function __VLS_template(): Partial<Record<string, (_: any) => any>>;
declare const __VLS_component: import('vue').DefineComponent<import('vue').ExtractPropTypes<{
    options: import('vue').PropType<CascaderOption[] | import('./types').CascaderOptionFunction>;
    multiple: BooleanConstructor;
    emptyValues: ArrayConstructor;
    valueOnClear: {
        readonly type: import('vue').PropType<string | number | boolean | Function>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: undefined;
    };
    size: {
        readonly type: import('vue').PropType<"" | "small" | "default" | "large">;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    placeholder: StringConstructor;
    disabled: BooleanConstructor;
    clearable: BooleanConstructor;
    filterable: BooleanConstructor;
    filterMethod: {
        readonly type: import('vue').PropType<(node: import('element-plus').CascaderNode, keyword: string) => boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: (node: import('element-plus').CascaderNode, keyword: string) => boolean;
    };
    separator: {
        readonly type: import('vue').PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: string;
    };
    showAllLevels: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: boolean;
    };
    collapseTags: BooleanConstructor;
    maxCollapseTags: {
        readonly type: import('vue').PropType<number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: number;
    };
    collapseTagsTooltip: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: boolean;
    };
    debounce: {
        readonly type: import('vue').PropType<number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: number;
    };
    beforeFilter: {
        readonly type: import('vue').PropType<(value: string) => boolean | Promise<any>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: () => true;
    };
    placement: {
        readonly type: import('vue').PropType<import('element-plus').Placement>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: string;
    };
    fallbackPlacements: {
        readonly type: import('vue').PropType<import('element-plus').Placement[]>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: string[];
    };
    popperClass: {
        readonly type: import('vue').PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: string;
    };
    teleported: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: true;
    };
    tagType: {
        default: string;
        type: import('vue').PropType<"success" | "warning" | "info" | "primary" | "danger">;
        required: false;
        validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    tagEffect: {
        default: string;
        type: import('vue').PropType<"dark" | "light" | "plain">;
        required: false;
        validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    validateEvent: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: boolean;
    };
    persistent: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: boolean;
    };
    modelValue: {
        readonly type: import('vue').PropType<(string | number)[] | (string | number) | ((string | number)[] | (string | number))[]>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    props: {
        readonly type: import('vue').PropType<import('element-plus').CascaderProps>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: () => import('element-plus').CascaderProps;
    };
}>, {
    reloadOptions: (params?: any) => void;
    cascaderRef: import('vue').Ref<ElCascaderInstance, ElCascaderInstance>;
}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {
    focus: (evt: FocusEvent) => void;
    blur: (evt: FocusEvent) => void;
    clear: () => void;
    "update:modelValue": (_: import('element-plus').CascaderValue) => void;
    change: (_: import('element-plus').CascaderValue) => void;
    visibleChange: (val: boolean) => void;
    expandChange: (val: import('element-plus').CascaderValue) => void;
    removeTag: (val: (string | number)[] | (string | number)) => void;
}, string, import('vue').PublicProps, Readonly<import('vue').ExtractPropTypes<{
    options: import('vue').PropType<CascaderOption[] | import('./types').CascaderOptionFunction>;
    multiple: BooleanConstructor;
    emptyValues: ArrayConstructor;
    valueOnClear: {
        readonly type: import('vue').PropType<string | number | boolean | Function>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: undefined;
    };
    size: {
        readonly type: import('vue').PropType<"" | "small" | "default" | "large">;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    placeholder: StringConstructor;
    disabled: BooleanConstructor;
    clearable: BooleanConstructor;
    filterable: BooleanConstructor;
    filterMethod: {
        readonly type: import('vue').PropType<(node: import('element-plus').CascaderNode, keyword: string) => boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: (node: import('element-plus').CascaderNode, keyword: string) => boolean;
    };
    separator: {
        readonly type: import('vue').PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: string;
    };
    showAllLevels: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: boolean;
    };
    collapseTags: BooleanConstructor;
    maxCollapseTags: {
        readonly type: import('vue').PropType<number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: number;
    };
    collapseTagsTooltip: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: boolean;
    };
    debounce: {
        readonly type: import('vue').PropType<number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: number;
    };
    beforeFilter: {
        readonly type: import('vue').PropType<(value: string) => boolean | Promise<any>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: () => true;
    };
    placement: {
        readonly type: import('vue').PropType<import('element-plus').Placement>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: string;
    };
    fallbackPlacements: {
        readonly type: import('vue').PropType<import('element-plus').Placement[]>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: string[];
    };
    popperClass: {
        readonly type: import('vue').PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: string;
    };
    teleported: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: true;
    };
    tagType: {
        default: string;
        type: import('vue').PropType<"success" | "warning" | "info" | "primary" | "danger">;
        required: false;
        validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    tagEffect: {
        default: string;
        type: import('vue').PropType<"dark" | "light" | "plain">;
        required: false;
        validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    validateEvent: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: boolean;
    };
    persistent: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: boolean;
    };
    modelValue: {
        readonly type: import('vue').PropType<(string | number)[] | (string | number) | ((string | number)[] | (string | number))[]>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    props: {
        readonly type: import('vue').PropType<import('element-plus').CascaderProps>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: () => import('element-plus').CascaderProps;
    };
}>> & Readonly<{
    onFocus?: ((evt: FocusEvent) => any) | undefined;
    onBlur?: ((evt: FocusEvent) => any) | undefined;
    onClear?: (() => any) | undefined;
    "onUpdate:modelValue"?: ((_: import('element-plus').CascaderValue) => any) | undefined;
    onChange?: ((_: import('element-plus').CascaderValue) => any) | undefined;
    onVisibleChange?: ((val: boolean) => any) | undefined;
    onExpandChange?: ((val: import('element-plus').CascaderValue) => any) | undefined;
    onRemoveTag?: ((val: (string | number)[] | (string | number)) => any) | undefined;
}>, {
    debounce: number;
    placement: import('element-plus').Placement;
    popperClass: string;
    teleported: boolean;
    clearable: boolean;
    disabled: boolean;
    separator: string;
    valueOnClear: string | number | boolean | Function;
    filterable: boolean;
    filterMethod: (node: import('element-plus').CascaderNode, keyword: string) => boolean;
    showAllLevels: boolean;
    collapseTags: boolean;
    maxCollapseTags: number;
    collapseTagsTooltip: boolean;
    beforeFilter: (value: string) => boolean | Promise<any>;
    fallbackPlacements: import('element-plus').Placement[];
    tagType: "success" | "warning" | "info" | "primary" | "danger";
    tagEffect: "dark" | "light" | "plain";
    validateEvent: boolean;
    persistent: boolean;
    props: import('element-plus').CascaderProps;
    multiple: boolean;
}, {}, {}, {}, string, import('vue').ComponentProvideOptions, true, {}, any>;
declare const _default: __VLS_WithTemplateSlots<typeof __VLS_component, ReturnType<typeof __VLS_template>>;
export default _default;
type __VLS_WithTemplateSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
