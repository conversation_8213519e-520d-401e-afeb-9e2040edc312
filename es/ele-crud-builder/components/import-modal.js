import { defineComponent, ref, watch, createBlock, openBlock, createSlots, withCtx, resolveDynamicComponent, createVNode, unref, createTextVNode } from "vue";
import { ElButton } from "element-plus";
import EleModal from "../../ele-modal/index";
import { deepCloneObject } from "../../ele-pro-form-builder/components/build-core";
import CodeEditer from "../../ele-pro-form-builder/components/code-editer";
import { itemsGenerateNewKey } from "../util";
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{ name: "ImportModal" },
  __name: "import-modal",
  props: {
    modelValue: { type: Boolean },
    config: {},
    isImport: { type: Boolean },
    jsonEditerComponent: { type: [String, Object, Function] }
  },
  emits: ["update:modelValue", "importData"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const jsonContent = ref("");
    const handleUpdateModelValue = (visible) => {
      emit("update:modelValue", visible);
    };
    const handleImportData = (data) => {
      emit("importData", data);
    };
    const handleCloseModal = () => {
      handleUpdateModelValue(false);
    };
    const handleImport = () => {
      if (!jsonContent.value) {
        return;
      }
      handleCloseModal();
      try {
        const result = JSON.parse(jsonContent.value);
        if (result && Array.isArray(result)) {
          const config = deepCloneObject(props.config);
          itemsGenerateNewKey(result, config.fields, false);
          if (config.fields) {
            result.forEach((item) => {
              config.fields.push(item);
            });
          } else {
            config.fields = result;
          }
          handleImportData(config);
          return;
        }
        if (result && typeof result === "object" && result.prop && result.fields == null) {
          const config = deepCloneObject(props.config);
          itemsGenerateNewKey(result, config.fields, false);
          if (config.fields) {
            config.fields.push(result);
          } else {
            config.fields = [result];
          }
          handleImportData(config);
          return;
        }
        if (result) {
          itemsGenerateNewKey(result.fields, [], false);
          handleImportData(result);
        }
      } catch (e) {
        console.error(e);
      }
    };
    watch(
      () => props.modelValue,
      (visible) => {
        if (visible && !props.isImport) {
          jsonContent.value = JSON.stringify(props.config || {}, void 0, 2);
        } else {
          jsonContent.value = "";
        }
      }
    );
    return (_ctx, _cache) => {
      return openBlock(), createBlock(EleModal, {
        width: 800,
        maxable: true,
        position: "center",
        title: "配置JSON",
        modelValue: _ctx.modelValue,
        closeOnClickModal: false,
        destroyOnClose: true,
        bodyStyle: {
          height: "520px",
          minHeight: "100%",
          maxHeight: "100%",
          padding: _ctx.isImport ? "8px 16px 8px 16px" : "8px 16px 12px 16px"
        },
        "onUpdate:modelValue": handleUpdateModelValue
      }, createSlots({
        default: withCtx(() => [
          (openBlock(), createBlock(resolveDynamicComponent(_ctx.jsonEditerComponent || CodeEditer), {
            modelValue: jsonContent.value,
            "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => jsonContent.value = $event)
          }, null, 8, ["modelValue"]))
        ]),
        _: 2
      }, [
        _ctx.isImport ? {
          name: "footer",
          fn: withCtx(() => [
            createVNode(unref(ElButton), {
              size: "default",
              onClick: handleCloseModal
            }, {
              default: withCtx(() => _cache[1] || (_cache[1] = [
                createTextVNode("取消")
              ])),
              _: 1,
              __: [1]
            }),
            createVNode(unref(ElButton), {
              type: "primary",
              size: "default",
              onClick: handleImport
            }, {
              default: withCtx(() => _cache[2] || (_cache[2] = [
                createTextVNode(" 导入 ")
              ])),
              _: 1,
              __: [2]
            })
          ]),
          key: "0"
        } : void 0
      ]), 1032, ["modelValue", "bodyStyle"]);
    };
  }
});
export {
  _sfc_main as default
};
