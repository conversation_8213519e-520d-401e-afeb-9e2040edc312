import { ElIconProps } from '../ele-app/el';
import { EleTooltipInstance } from '../ele-app/plus';

declare function __VLS_template(): {
    reference?(_: {}): any;
    title?(_: {}): any;
    content?(_: {}): any;
    actions?(_: {
        cancel: (e: MouseEvent) => void;
        confirm: (e: MouseEvent) => void;
        cancelText: string;
        confirmText: string;
    }): any;
    action?(_: {
        cancel: (e: MouseEvent) => void;
        confirm: (e: MouseEvent) => void;
        cancelText: string;
        confirmText: string;
    }): any;
};
declare const __VLS_component: import('vue').DefineComponent<import('vue').ExtractPropTypes<{
    trigger: {
        type: import('vue').PropType<import('../ele-app/plus').ElePopoverProps["trigger"]>;
        default: string;
    };
    confirmButtonText: StringConstructor;
    cancelButtonText: StringConstructor;
    confirmButtonType: {
        type: import('vue').PropType<import('../ele-app/el').ElPopconfirmProps["confirmButtonType"]>;
        default: string;
    };
    cancelButtonType: {
        type: import('vue').PropType<import('../ele-app/el').ElPopconfirmProps["cancelButtonType"]>;
        default: string;
    };
    icon: import('vue').PropType<import('../ele-app/el').ElPopconfirmProps["icon"]>;
    iconColor: {
        type: StringConstructor;
        default: string;
    };
    hideIcon: BooleanConstructor;
    hideConfirmButton: BooleanConstructor;
    hideCancelButton: BooleanConstructor;
    iconStyle: import('vue').PropType<import('../ele-app/types').StyleValue>;
    iconProps: import('vue').PropType<ElIconProps>;
    confirmButtonProps: import('vue').PropType<import('../ele-app/el').ElButtonProps>;
    cancelButtonProps: import('vue').PropType<import('../ele-app/el').ElButtonProps>;
    footerStyle: import('vue').PropType<import('../ele-app/types').StyleValue>;
    transition: {
        type: StringConstructor;
        default: string;
    };
    bodyClass: StringConstructor;
    bodyStyle: import('vue').PropType<import('../ele-app/types').StyleValue>;
    titleStyle: import('vue').PropType<import('../ele-app/types').StyleValue>;
    contentStyle: import('vue').PropType<import('../ele-app/types').StyleValue>;
    title: StringConstructor;
    effect: {
        readonly default: "light";
        readonly type: import('vue').PropType<import('element-plus').PopperEffect>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        readonly __epPropKey: true;
    };
    placement: {
        readonly type: import('vue').PropType<import('element-plus').Placement>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: "bottom";
    };
    teleported: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: true;
    };
    appendTo: {
        readonly type: import('vue').PropType<string | HTMLElement>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    disabled: BooleanConstructor;
    offset: {
        readonly type: import('vue').PropType<number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: undefined;
    };
    persistent: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: true;
    };
    width: {
        readonly type: import('vue').PropType<string | number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: 150;
    };
    visible: {
        readonly type: import('vue').PropType<boolean | null>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: null;
    };
    triggerKeys: {
        readonly type: import('vue').PropType<string[]>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: () => string[];
    };
    popperOptions: {
        readonly type: import('vue').PropType<Partial<import('element-plus').Options>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: () => {};
    };
    tabindex: {
        readonly type: import('vue').PropType<string | number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: 0;
    };
    showArrow: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: true;
    };
    content: {
        readonly type: import('vue').PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: "";
    };
    enterable: {
        readonly default: true;
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        readonly __epPropKey: true;
    };
    showAfter: {
        readonly type: import('vue').PropType<number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: 0;
    };
    hideAfter: {
        readonly type: import('vue').PropType<number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: 200;
    };
    autoClose: {
        readonly type: import('vue').PropType<number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: 0;
    };
    popperStyle: import('vue').PropType<import('../ele-app/types').StyleValue>;
    popperClass: StringConstructor;
    ariaLabel: StringConstructor;
    bg: StringConstructor;
    zIndex: NumberConstructor;
    arrowOffset: {
        readonly type: import('vue').PropType<number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: 5;
    };
    virtualRef: {
        readonly type: import('vue').PropType<import('element-plus').Measurable>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    virtualTriggering: BooleanConstructor;
    className: StringConstructor;
    gpuAcceleration: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: true;
    };
    arrowBg: StringConstructor;
}>, {
    tooltipRef: import('vue').Ref<EleTooltipInstance, EleTooltipInstance>;
    hidePopper: () => void;
}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {
    cancel: (_e: MouseEvent) => void;
    "update:visible": (value: boolean) => void;
    "before-enter": () => void;
    "before-leave": () => void;
    "after-enter": () => void;
    "after-leave": () => void;
    confirm: (_e: MouseEvent) => void;
}, string, import('vue').PublicProps, Readonly<import('vue').ExtractPropTypes<{
    trigger: {
        type: import('vue').PropType<import('../ele-app/plus').ElePopoverProps["trigger"]>;
        default: string;
    };
    confirmButtonText: StringConstructor;
    cancelButtonText: StringConstructor;
    confirmButtonType: {
        type: import('vue').PropType<import('../ele-app/el').ElPopconfirmProps["confirmButtonType"]>;
        default: string;
    };
    cancelButtonType: {
        type: import('vue').PropType<import('../ele-app/el').ElPopconfirmProps["cancelButtonType"]>;
        default: string;
    };
    icon: import('vue').PropType<import('../ele-app/el').ElPopconfirmProps["icon"]>;
    iconColor: {
        type: StringConstructor;
        default: string;
    };
    hideIcon: BooleanConstructor;
    hideConfirmButton: BooleanConstructor;
    hideCancelButton: BooleanConstructor;
    iconStyle: import('vue').PropType<import('../ele-app/types').StyleValue>;
    iconProps: import('vue').PropType<ElIconProps>;
    confirmButtonProps: import('vue').PropType<import('../ele-app/el').ElButtonProps>;
    cancelButtonProps: import('vue').PropType<import('../ele-app/el').ElButtonProps>;
    footerStyle: import('vue').PropType<import('../ele-app/types').StyleValue>;
    transition: {
        type: StringConstructor;
        default: string;
    };
    bodyClass: StringConstructor;
    bodyStyle: import('vue').PropType<import('../ele-app/types').StyleValue>;
    titleStyle: import('vue').PropType<import('../ele-app/types').StyleValue>;
    contentStyle: import('vue').PropType<import('../ele-app/types').StyleValue>;
    title: StringConstructor;
    effect: {
        readonly default: "light";
        readonly type: import('vue').PropType<import('element-plus').PopperEffect>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        readonly __epPropKey: true;
    };
    placement: {
        readonly type: import('vue').PropType<import('element-plus').Placement>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: "bottom";
    };
    teleported: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: true;
    };
    appendTo: {
        readonly type: import('vue').PropType<string | HTMLElement>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    disabled: BooleanConstructor;
    offset: {
        readonly type: import('vue').PropType<number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: undefined;
    };
    persistent: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: true;
    };
    width: {
        readonly type: import('vue').PropType<string | number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: 150;
    };
    visible: {
        readonly type: import('vue').PropType<boolean | null>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: null;
    };
    triggerKeys: {
        readonly type: import('vue').PropType<string[]>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: () => string[];
    };
    popperOptions: {
        readonly type: import('vue').PropType<Partial<import('element-plus').Options>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: () => {};
    };
    tabindex: {
        readonly type: import('vue').PropType<string | number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: 0;
    };
    showArrow: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: true;
    };
    content: {
        readonly type: import('vue').PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: "";
    };
    enterable: {
        readonly default: true;
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        readonly __epPropKey: true;
    };
    showAfter: {
        readonly type: import('vue').PropType<number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: 0;
    };
    hideAfter: {
        readonly type: import('vue').PropType<number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: 200;
    };
    autoClose: {
        readonly type: import('vue').PropType<number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: 0;
    };
    popperStyle: import('vue').PropType<import('../ele-app/types').StyleValue>;
    popperClass: StringConstructor;
    ariaLabel: StringConstructor;
    bg: StringConstructor;
    zIndex: NumberConstructor;
    arrowOffset: {
        readonly type: import('vue').PropType<number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: 5;
    };
    virtualRef: {
        readonly type: import('vue').PropType<import('element-plus').Measurable>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    virtualTriggering: BooleanConstructor;
    className: StringConstructor;
    gpuAcceleration: {
        readonly type: import('vue').PropType<boolean>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    } & {
        readonly default: true;
    };
    arrowBg: StringConstructor;
}>> & Readonly<{
    onCancel?: ((_e: MouseEvent) => any) | undefined;
    "onUpdate:visible"?: ((value: boolean) => any) | undefined;
    "onBefore-enter"?: (() => any) | undefined;
    "onBefore-leave"?: (() => any) | undefined;
    "onAfter-enter"?: (() => any) | undefined;
    "onAfter-leave"?: (() => any) | undefined;
    onConfirm?: ((_e: MouseEvent) => any) | undefined;
}>, {
    effect: import('element-plus').PopperEffect;
    placement: import('element-plus').Placement;
    teleported: boolean;
    disabled: boolean;
    offset: number;
    persistent: boolean;
    width: string | number;
    visible: boolean | null;
    trigger: (import('element-plus').TooltipTriggerType | import('element-plus').TooltipTriggerType[]) | undefined;
    triggerKeys: string[];
    popperOptions: Partial<import('element-plus').Options>;
    tabindex: string | number;
    showArrow: boolean;
    transition: string;
    content: string;
    enterable: boolean;
    showAfter: number;
    hideAfter: number;
    autoClose: number;
    arrowOffset: number;
    virtualTriggering: boolean;
    gpuAcceleration: boolean;
    confirmButtonType: ("" | "success" | "warning" | "info" | "default" | "primary" | "danger" | "text") | undefined;
    cancelButtonType: ("" | "success" | "warning" | "info" | "default" | "primary" | "danger" | "text") | undefined;
    iconColor: string;
    hideIcon: boolean;
    hideConfirmButton: boolean;
    hideCancelButton: boolean;
}, {}, {}, {}, string, import('vue').ComponentProvideOptions, true, {}, any>;
declare const _default: __VLS_WithTemplateSlots<typeof __VLS_component, ReturnType<typeof __VLS_template>>;
export default _default;
type __VLS_WithTemplateSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
