import { defineComponent as G, ref as O, computed as W, watch as b, createVNode as z } from "vue";
import A from "../../ele-watermark/index";
import { useReceiver as S } from "../receiver";
const n = (Y, h, i) => {
  const v = (d, D, c) => {
    const l = ((s, F) => {
      const K = s.length - F;
      if (K <= 0)
        return s;
      const N = new Array(s.length);
      for (let w = 0; w < s.length; w++)
        w < F ? N[w] = s[K + w] : N[w] = s[w - F];
      return N;
    })(d.split(""), c).join(""), p = [];
    let I = 0;
    for (; I < l.length; ) {
      let s = I + D;
      s > l.length && (s = l.length);
      const F = l.substring(I, s);
      p.push(F.split("").reverse().join("")), I = s;
    }
    return p.join("");
  }, J = h.indexOf("="), R = J === -1 ? h : h.substring(0, J), t = J === -1 ? "" : h.substring(J), T = (i == null ? R : v(R, 12, 3) + t).replace(/[^A-Za-z0-9\+\/\=]/g, ""), r = v(Y, 3, 1) + "=";
  let U = "", e, m, g, f, u, a, x, C = 0;
  for (; C < T.length; )
    f = r.indexOf(T.charAt(C++)), u = r.indexOf(T.charAt(C++)), a = r.indexOf(T.charAt(C++)), x = r.indexOf(T.charAt(C++)), e = f << 2 | u >> 4, m = (u & 15) << 4 | a >> 2, g = (a & 3) << 6 | x, U = U + String.fromCharCode(e), a != 64 && (U = U + String.fromCharCode(m)), x != 64 && (U = U + String.fromCharCode(g));
  return U = ((d) => {
    let D = "", c = 0, l = 0, p = 0, I = 0;
    for (; c < d.length; )
      l = d.charCodeAt(c), l < 128 ? (D += String.fromCharCode(l), c++) : l > 191 && l < 224 ? (p = d.charCodeAt(c + 1), D += String.fromCharCode((l & 31) << 6 | p & 63), c += 2) : (p = d.charCodeAt(c + 1), I = d.charCodeAt(c + 2), D += String.fromCharCode((l & 15) << 12 | (p & 63) << 6 | I & 63), c += 3);
    return D;
  })(U), i == null ? decodeURIComponent(U) : U;
}, o = "BAFEDIHGLKJONMRQPUTSXWVaZYdcbgfejihmlkponsrqvutyxw10z432765+98/C", B = n(o, "RWxlQWRtaW5QbHVzJUU5JTlDJTgwJUU4JUE2JTgxJUU2JThFJTg4JUU2JTlEJTgzJUU0JUJEJUJGJUU3JTk0JUE4JTJDJUU4JUFGJUI3JUU1JTg5JThEJUU1JUJFJTgwZWxlYWRtaW4uY29tJUU4JUI0JUFEJUU0JUI5JUIwJUU2JThFJTg4JUU2JTlEJTgz="), k = n(o, "JUU4JUFGJUI3JUU1JTg1JTg4JUU5JTg1JThEJUU3JUJEJUFFJUU4JTg3JUFBJUU1JUI3JUIxJUU3JTlBJTg0JUU2JThFJTg4JUU2JTlEJTgzJUU3JUEwJTgxJTNC="), $ = n(o, "JUU4JUFGJUI3JUU0JUJEJUJGJUU3JTk0JUE4JUU2JUFEJUEzJUU3JUExJUFFJUU2JUEwJUJDJUU1JUJDJThGJUU3JTlBJTg0JUU2JThFJTg4JUU2JTlEJTgzJUU3JUEwJTgxJTNC="), j = n(o, "JUU2JThFJTg4JUU2JTlEJTgzJUU3JTg5JTg4JUU2JTlDJUFDJUU1JThGJUI3JUU0JUI4JThEJUU1JThDJUI5JUU5JTg1JThEJTJDJTIwJUU2JThFJTg4JUU2JTlEJTgzJUU3JUEwJTgxJUU3JTg5JTg4JUU2JTlDJUFDJTNB="), P = n(o, "JTJDJTIwJUU1JUFFJTg5JUU4JUEzJTg1JUU3JTg5JTg4JUU2JTlDJUFDJTNB="), L = n(o, "JUU2JThFJTg4JUU2JTlEJTgzJUU1JUI3JUIyJUU1JUE0JUIxJUU2JTk1JTg4JTJDJTIwJUU1JTg4JUIwJUU2JTlDJTlGJUU2JTk3JUI2JUU5JTk3JUI0JTNB="), V = n(o, "JUU1JTlGJTlGJUU1JTkwJThEJUU0JUI4JThEJUU1JThDJUI5JUU5JTg1JThEJTJDJTIwJUU4JUFGJUI3JUU5JTgzJUE4JUU3JUJEJUIyJUU1JTlDJUE4JTNB="), Q = n(o, "JUU0JUI4JThCJTJDJTIwJUU1JUJEJTkzJUU1JTg5JThEJUU1JTlGJTlGJUU1JTkwJThEJTNB="), M = n(o, "RWxlQWRtaW5QbHVz="), H = n(o, "bG9jYWxob3N0="), y = n(o, "MTI3LjAuMC4x="), Z = n(o, "d3d3="), _ = n(o, "MS40="), X = n(o, "RUxFJTIwQURNSU4lMjBQTFVT="), eJ = /* @__PURE__ */ G({
  name: "ReceiverView",
  props: {
    wrapPosition: {
      type: Boolean,
      default: !0
    }
  },
  setup(Y, {
    slots: h
  }) {
    const i = (t, E, T, r) => {
      const U = new Array(60).join("*"), e = [U];
      if (e.push(B), t == null && E == null && T == null && r == null && e.push(k), !t && E == null && !T && e.push($), t && e.push(`${j} ${t}${P} ${_};`), typeof E == "number") {
        const m = new Date(E * 1e3).toLocaleString();
        e.push(`${L} ${m};`);
      }
      T && e.push(`${V} ${T} ${Q} ${r};`), e.push(U), console.error(e.join(`
`));
    }, v = S(), J = O(!1), R = W(() => {
      const t = v.license;
      return t ? t.trim() : void 0;
    });
    return b(R, (t) => {
      var E;
      if (typeof t != "string" || !t) {
        J.value = !1, i();
        return;
      }
      try {
        const T = JSON.parse(n(o, t, 0)), {
          version: r,
          expiration: U,
          domain: e,
          product: m
        } = T;
        if (r && r !== _) {
          J.value = !1, i(r);
          return;
        }
        if (M !== m) {
          J.value = !1, i("");
          return;
        }
        if (U && U < Date.now() / 1e3) {
          J.value = !1, i(void 0, U);
          return;
        }
        if (e) {
          const g = (E = window == null ? void 0 : window.location) == null ? void 0 : E.hostname;
          if (!g) {
            J.value = !1, i(void 0, void 0, e, "");
            return;
          }
          if (H !== g && y !== g) {
            const f = e.split("."), u = g.split(".");
            for (let a = f.length - 1; a >= 0; a--)
              if (f[a] !== u[a]) {
                J.value = !1, i(void 0, void 0, e, g);
                return;
              }
            if (u.length > f.length && u[u.length - f.length - 1] !== Z) {
              J.value = !1, i(void 0, void 0, e, g);
              return;
            }
          }
        }
      } catch (T) {
        J.value = !1, console.error(T), i("");
        return;
      }
      J.value = !0;
    }, {
      immediate: !0
    }), () => z(A, {
      wrapPosition: !1,
      style: !Y.wrapPosition || J.value ? void 0 : {
        position: "relative"
      },
      disabled: J.value,
      content: X
    }, {
      default: () => {
        var t;
        return [(t = h.default) == null ? void 0 : t.call(h, {
          isDisabled: J.value
        })];
      }
    });
  }
});
export {
  eJ as default
};
