// 工作的 EleAdmin Plus License 生成器
// 通过分析源码，我们知道需要的 JSON 格式

const KEY_ENCRYPT =
  'BAFEDIHGLKJONMRQPUTSXWVaZYdcbgfejihmlkponsrqvutyxw10z432765+98/C';

// 从源码复制的解码函数
const DECODE_STR = (key, encrypt, decode) => {
  const format = (encrypt, length, offset) => {
    const content = ((array, offset) => {
      const max = array.length - offset;
      if (max <= 0) {
        return array;
      }
      const result = new Array(array.length);
      for (let i = 0; i < array.length; i++) {
        if (i < offset) {
          result[i] = array[max + i];
        } else {
          result[i] = array[i - offset];
        }
      }
      return result;
    })(encrypt.split(''), offset).join('');
    const sb = [];
    let start = 0;
    while (start < content.length) {
      let end = start + length;
      if (end > content.length) {
        end = content.length;
      }
      const item = content.substring(start, end);
      sb.push(item.split('').reverse().join(''));
      start = end;
    }
    return sb.join('');
  };
  const index = encrypt.indexOf('=');
  const body = index === -1 ? encrypt : encrypt.substring(0, index);
  const suffix = index === -1 ? '' : encrypt.substring(index);
  const temp = decode == null ? body : format(body, 12, 3) + suffix;
  const input = temp.replace(/[^A-Za-z0-9\+\/\=]/g, '');
  const KEYS = format(key, 3, 1) + '=';
  let output = '';
  let chr1, chr2, chr3;
  let enc1, enc2, enc3, enc4;
  let i = 0;
  while (i < input.length) {
    enc1 = KEYS.indexOf(input.charAt(i++));
    enc2 = KEYS.indexOf(input.charAt(i++));
    enc3 = KEYS.indexOf(input.charAt(i++));
    enc4 = KEYS.indexOf(input.charAt(i++));
    chr1 = (enc1 << 2) | (enc2 >> 4);
    chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
    chr3 = ((enc3 & 3) << 6) | enc4;
    output = output + String.fromCharCode(chr1);
    if (enc3 != 64) {
      output = output + String.fromCharCode(chr2);
    }
    if (enc4 != 64) {
      output = output + String.fromCharCode(chr3);
    }
  }
  output = ((utftext) => {
    let string = '';
    let i = 0;
    let c = 0;
    let c2 = 0;
    let c3 = 0;
    while (i < utftext.length) {
      c = utftext.charCodeAt(i);
      if (c < 128) {
        string += String.fromCharCode(c);
        i++;
      } else if (c > 191 && c < 224) {
        c2 = utftext.charCodeAt(i + 1);
        string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
        i += 2;
      } else {
        c2 = utftext.charCodeAt(i + 1);
        c3 = utftext.charCodeAt(i + 2);
        string += String.fromCharCode(
          ((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)
        );
        i += 3;
      }
    }
    return string;
  })(output);
  return decode == null ? decodeURIComponent(output) : output;
};

// 解码已知常量
const PRODUCT_KEY = DECODE_STR(KEY_ENCRYPT, 'RWxlQWRtaW5QbHVz='); // EleAdminPlus
const V_KEY = DECODE_STR(KEY_ENCRYPT, 'MS40='); // 1.4

console.log('Product Key:', PRODUCT_KEY);
console.log('Version Key:', V_KEY);

// 手动构造一个有效的 license
// 我们需要创建一个 JSON: {"version":"1.4","product":"EleAdminPlus"}

// 让我们尝试一个更简单的方法：直接使用 Node.js 的 Buffer 来编码
function createValidLicense() {
  const licenseData = {
    version: '1.4',
    product: 'EleAdminPlus'
  };

  const jsonStr = JSON.stringify(licenseData);
  console.log('Target JSON:', jsonStr);

  // 使用 URL 编码
  const encoded = encodeURIComponent(jsonStr);
  console.log('URL encoded:', encoded);

  // 转换为 Base64
  const base64 = Buffer.from(encoded, 'utf8').toString('base64');
  console.log('Base64:', base64);

  // 转换为自定义编码
  const format = (encrypt, length, offset) => {
    const content = ((array, offset) => {
      const max = array.length - offset;
      if (max <= 0) {
        return array;
      }
      const result = new Array(array.length);
      for (let i = 0; i < array.length; i++) {
        if (i < offset) {
          result[i] = array[max + i];
        } else {
          result[i] = array[i - offset];
        }
      }
      return result;
    })(encrypt.split(''), offset).join('');
    const sb = [];
    let start = 0;
    while (start < content.length) {
      let end = start + length;
      if (end > content.length) {
        end = content.length;
      }
      const item = content.substring(start, end);
      sb.push(item.split('').reverse().join(''));
      start = end;
    }
    return sb.join('');
  };

  const standardKeys =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
  const customKeys = format(KEY_ENCRYPT, 3, 1) + '=';

  console.log('Standard keys:', standardKeys);
  console.log('Custom keys:', customKeys);
  console.log('Custom keys length:', customKeys.length);
  console.log('Standard keys length:', standardKeys.length);

  let result = '';
  for (let i = 0; i < base64.length; i++) {
    const char = base64[i];
    const index = standardKeys.indexOf(char);
    if (index !== -1) {
      result += customKeys[index];
    } else {
      result += char;
    }
  }

  console.log('Custom encoded:', result);
  return result;
}

const license = createValidLicense();

// 验证生成的 license
function validateLicense(license) {
  try {
    const decoded = DECODE_STR(KEY_ENCRYPT, license, 0);
    console.log('\nDecoded string:', decoded);
    const data = JSON.parse(decoded);
    console.log('Decoded License Data:', data);
    return data;
  } catch (e) {
    console.error('Invalid license:', e);
    return null;
  }
}

console.log('\n=== 验证生成的 License ===');
const result = validateLicense(license);

console.log('\n=== 验证用户提供的有效 License ===');
const userLicense =
  'dk9mcwJyetRWQlxWRiojIiwiIzVHbQ5Wa6ICdjVmaiV3culWYt9GZiwSMl5yc1xGciojIj5ibp1GZhVGb6ICZpJCLi02bEp1UGBVShhkIvl2cyVmdiwiIgICNuEjI6IibQf0NW==';
const userResult = validateLicense(userLicense);

if (result) {
  console.log('\n✅ License 验证成功！');
  console.log('\n=== 使用说明 ===');
  console.log('将以下 license 添加到你的 EleConfigProvider 组件中：');
  console.log('\nLicense:', license);
  console.log('\n示例代码：');
  console.log('<EleConfigProvider license="' + license + '">');
  console.log('  <!-- 你的应用内容 -->');
  console.log('</EleConfigProvider>');
} else {
  console.log('\n❌ License 验证失败！');
}
