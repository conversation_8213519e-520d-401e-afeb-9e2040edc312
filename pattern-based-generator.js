// 基于模式的 License 生成器
// 通过分析已有的有效 license 来生成新的

const KEY_ENCRYPT = 'BAFEDIHGLKJONMRQPUTSXWVaZYdcbgfejihmlkponsrqvutyxw10z432765+98/C';

// 解码函数
const DECODE_STR = (key, encrypt, decode) => {
  const format = (encrypt, length, offset) => {
    const content = ((array, offset) => {
      const max = array.length - offset;
      if (max <= 0) {
        return array;
      }
      const result = new Array(array.length);
      for (let i = 0; i < array.length; i++) {
        if (i < offset) {
          result[i] = array[max + i];
        } else {
          result[i] = array[i - offset];
        }
      }
      return result;
    })(encrypt.split(''), offset).join('');
    const sb = [];
    let start = 0;
    while (start < content.length) {
      let end = start + length;
      if (end > content.length) {
        end = content.length;
      }
      const item = content.substring(start, end);
      sb.push(item.split('').reverse().join(''));
      start = end;
    }
    return sb.join('');
  };
  const index = encrypt.indexOf('=');
  const body = index === -1 ? encrypt : encrypt.substring(0, index);
  const suffix = index === -1 ? '' : encrypt.substring(index);
  const temp = decode == null ? body : format(body, 12, 3) + suffix;
  const input = temp.replace(/[^A-Za-z0-9\+\/\=]/g, '');
  const KEYS = format(key, 3, 1) + '=';
  let output = '';
  let chr1, chr2, chr3;
  let enc1, enc2, enc3, enc4;
  let i = 0;
  while (i < input.length) {
    enc1 = KEYS.indexOf(input.charAt(i++));
    enc2 = KEYS.indexOf(input.charAt(i++));
    enc3 = KEYS.indexOf(input.charAt(i++));
    enc4 = KEYS.indexOf(input.charAt(i++));
    chr1 = (enc1 << 2) | (enc2 >> 4);
    chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
    chr3 = ((enc3 & 3) << 6) | enc4;
    output = output + String.fromCharCode(chr1);
    if (enc3 != 64) {
      output = output + String.fromCharCode(chr2);
    }
    if (enc4 != 64) {
      output = output + String.fromCharCode(chr3);
    }
  }
  output = ((utftext) => {
    let string = '';
    let i = 0;
    let c = 0;
    let c2 = 0;
    let c3 = 0;
    while (i < utftext.length) {
      c = utftext.charCodeAt(i);
      if (c < 128) {
        string += String.fromCharCode(c);
        i++;
      } else if (c > 191 && c < 224) {
        c2 = utftext.charCodeAt(i + 1);
        string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
        i += 2;
      } else {
        c2 = utftext.charCodeAt(i + 1);
        c3 = utftext.charCodeAt(i + 2);
        string += String.fromCharCode(
          ((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)
        );
        i += 3;
      }
    }
    return string;
  })(output);
  return decode == null ? decodeURIComponent(output) : output;
};

// 验证函数
function validateLicense(license) {
  try {
    const decoded = DECODE_STR(KEY_ENCRYPT, license, 0);
    const data = JSON.parse(decoded);
    return data;
  } catch (e) {
    return null;
  }
}

// 已知的有效 license 模式
const VALID_PATTERNS = {
  // License 1: 带域名限制
  pattern1: {
    license: 'dk9mcwJyetRWQlxWRiojIiwiIzVHbQ5Wa6ICdjVmaiV3culWYt9GZiwSMl5yc1xGciojIj5ibp1GZhVGb6ICZpJCLi02bEp1UGBVShhkIvl2cyVmdiwiIgICNuEjI6IibQf0NW==',
    data: {"product":"EleAdminPlus","subject":1,"domain":"plus.eleadmin.com","id":"HaIPFSZD","version":"1.4"}
  },
  // License 2: 无域名限制
  pattern2: {
    license: 'I6ICZpJyewMjMykzMwYzMiAjN1YDOxkjM0NWdk9mcwJCLtRWQlxWRiojIiwiIzVHbQ5WaiojIulWYt9GZvl2cyVmdiwiI9JCNuEjI6Iib5Ej',
    data: {"id":"1936039223029186560","product":"EleAdminPlus","domain":"","version":"1.4"}
  }
};

// 生成随机 ID
function generateRandomId() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 生成随机数字 ID
function generateRandomNumId() {
  return Math.floor(Math.random() * 9000000000000000000) + 1000000000000000000;
}

// 基于模式生成新的 license
function generateLicenseFromPattern(patternType = 'pattern2', customOptions = {}) {
  const pattern = VALID_PATTERNS[patternType];
  if (!pattern) {
    throw new Error('Invalid pattern type');
  }

  // 创建新的数据对象
  const newData = { ...pattern.data };
  
  // 应用自定义选项
  if (customOptions.id !== undefined) {
    newData.id = customOptions.id.toString();
  } else {
    // 生成新的随机 ID
    newData.id = patternType === 'pattern1' ? generateRandomId() : generateRandomNumId().toString();
  }
  
  if (customOptions.domain !== undefined) {
    newData.domain = customOptions.domain;
  }
  
  if (customOptions.subject !== undefined) {
    newData.subject = customOptions.subject;
  }

  return newData;
}

// 手动构造 license（基于已知模式的字符替换）
function createLicenseByPattern(data, basePattern) {
  // 这是一个简化的方法，通过修改已知有效 license 的部分字符来生成新的
  // 实际上这种方法不是完全可靠的，但可以作为临时解决方案
  
  const baseLicense = VALID_PATTERNS[basePattern].license;
  const baseData = VALID_PATTERNS[basePattern].data;
  
  // 如果数据结构相同，可以尝试简单的字符替换
  if (JSON.stringify(Object.keys(data).sort()) === JSON.stringify(Object.keys(baseData).sort())) {
    // 生成一些变化
    let newLicense = baseLicense;
    
    // 简单的字符替换策略（这不是完美的方法，但可能有效）
    const oldId = baseData.id;
    const newId = data.id;
    
    // 尝试一些简单的替换
    if (oldId !== newId) {
      // 替换一些字符来反映 ID 的变化
      const replaceMap = {
        'a': 'b', 'b': 'c', 'c': 'd', 'd': 'e', 'e': 'f',
        'f': 'g', 'g': 'h', 'h': 'i', 'i': 'j', 'j': 'k',
        '0': '1', '1': '2', '2': '3', '3': '4', '4': '5'
      };
      
      // 随机替换一些字符
      const chars = newLicense.split('');
      const positions = [10, 15, 20, 25, 30]; // 一些安全的位置
      
      positions.forEach(pos => {
        if (pos < chars.length && replaceMap[chars[pos]]) {
          chars[pos] = replaceMap[chars[pos]];
        }
      });
      
      newLicense = chars.join('');
    }
    
    return newLicense;
  }
  
  return null;
}

// 生成多个新的 license
console.log('=== 基于模式的 License 生成器 ===\n');

// 验证原始 license
console.log('验证原始 License:');
Object.entries(VALID_PATTERNS).forEach(([key, pattern]) => {
  const result = validateLicense(pattern.license);
  console.log(`${key}: ${result ? '✅ 有效' : '❌ 无效'}`);
});

console.log('\n=== 生成新的 License ===\n');

// 生成基于 pattern2 的新 license（无域名限制）
const newLicenses = [];

for (let i = 1; i <= 5; i++) {
  console.log(`${i}. 生成新的无域名限制 License:`);
  
  const newData = generateLicenseFromPattern('pattern2', {
    id: generateRandomNumId()
  });
  
  console.log('   数据:', JSON.stringify(newData));
  
  // 尝试基于模式生成
  const newLicense = createLicenseByPattern(newData, 'pattern2');
  
  if (newLicense) {
    console.log('   License:', newLicense);
    
    // 验证生成的 license
    const isValid = validateLicense(newLicense);
    console.log('   验证:', isValid ? '✅ 有效' : '❌ 无效');
    
    if (isValid) {
      newLicenses.push(newLicense);
    }
  } else {
    console.log('   ❌ 生成失败');
  }
  
  console.log('');
}

// 显示所有有效的 license
console.log('=== 可用的 License 列表 ===\n');

const allValidLicenses = [
  VALID_PATTERNS.pattern1.license,
  VALID_PATTERNS.pattern2.license,
  ...newLicenses
];

allValidLicenses.forEach((license, index) => {
  console.log(`License ${index + 1}:`);
  console.log(license);
  console.log('');
});

console.log('=== 使用说明 ===');
console.log('将任意一个 License 添加到你的 EleConfigProvider 中：');
console.log('');
console.log('<EleConfigProvider :license="LICENSE_CODE">');
console.log('  <!-- 你的应用内容 -->');
console.log('</EleConfigProvider>');
console.log('');
console.log('推荐使用 License 2（无域名限制）：');
console.log(VALID_PATTERNS.pattern2.license);

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    VALID_PATTERNS,
    generateLicenseFromPattern,
    validateLicense,
    allValidLicenses
  };
}
