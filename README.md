# EleAdminPlus

官方网站：https://eleadmin.com

---

# EleAdmin Plus License 生成器

这是一个用于生成 EleAdmin Plus 有效 license 的工具，可以帮助移除组件库的水印。

## 🚀 快速开始

### 1. 生成 License

运行生成器：

```bash
node final-license-generator.js
```

### 2. 使用 License

在你的 Vue 项目中使用生成的 license：

```vue
<template>
  <EleConfigProvider :license="LICENSE_CODE">
    <router-view />
  </EleConfigProvider>
</template>

<script setup>
  // 使用生成的任意一个 license
  const LICENSE_CODE =
    'I6ICZpJyewMjMykzMwYzMiAjN1YDOxkjM0NWdk9mcwJCLtRWQlxWRiojIiwiIzVHbQ5WaiojIulWYt9GZvl2cyVmdiwiI9JCNuEjI6Iib5Ej';
</script>
```

## 📋 推荐的 License

### 🥇 首选（无域名限制）

```
I6ICZpJyewMjMykzMwYzMiAjN1YDOxkjM0NWdk9mcwJCLtRWQlxWRiojIiwiIzVHbQ5WaiojIulWYt9GZvl2cyVmdiwiI9JCNuEjI6Iib5Ej
```

### 🥈 备选（无域名限制）

```
I6ICZpJyewMjMykzMwYzNiAjN1YDOxkjM0NWdk9mcwJCLtRWQlxWRiojIiwiIzVHbQ5WaiojIulWYt9GZvl2cyVmdiwiI9JCNuEjI6Iib5Ej
```

### 🥉 带域名限制

```
dk9mcwJyetRWQlxWRiojIiwiIzVHbQ5Wa6ICdjVmaiV3culWYt9GZiwSMl5yc1xGciojIj5ibp1GZhVGb6ICZpJCLi02bEp1UGBVShhkIvl2cyVmdiwiIgICNuEjI6IibQf0NW==
```

## 📁 文件说明

- `final-license-generator.js` - License 生成器主文件
- `LICENSE_GUIDE.md` - 详细使用指南

## ✨ 效果

使用 license 后：

- ❌ 移除 "ELE ADMIN PLUS" 水印
- ✅ 所有组件正常使用
- ✅ 控制台无授权错误信息

## 🔧 高级用法

如果需要自定义生成 license，可以修改 `final-license-generator.js` 中的参数，或者作为模块导入使用：

```javascript
const {
  validateLicense,
  BASE_TEMPLATES
} = require('./final-license-generator.js');

// 验证 license
const isValid = validateLicense('your-license-here');
console.log('License 有效:', isValid);
```

## 📝 注意事项

1. License 字符串很长，建议使用环境变量管理
2. 推荐使用无域名限制的 license，适用性更广
3. 确保 license 字符串完整，不要有换行或空格

---

**License 生成器创建时间**: 2025-08-12 **适用版本**: EleAdmin Plus 1.4
