// EleAdmin Plus License Generator
// 用于生成 ele-config-provider 组件的有效 license

const DECODE_STR = (key, encrypt, decode) => {
  const format = (encrypt, length, offset) => {
    const content = ((array, offset) => {
      const max = array.length - offset;
      if (max <= 0) {
        return array;
      }
      const result = new Array(array.length);
      for (let i = 0; i < array.length; i++) {
        if (i < offset) {
          result[i] = array[max + i];
        } else {
          result[i] = array[i - offset];
        }
      }
      return result;
    })(encrypt.split(''), offset).join('');
    const sb = [];
    let start = 0;
    while (start < content.length) {
      let end = start + length;
      if (end > content.length) {
        end = content.length;
      }
      const item = content.substring(start, end);
      sb.push(item.split('').reverse().join(''));
      start = end;
    }
    return sb.join('');
  };
  const index = encrypt.indexOf('=');
  const body = index === -1 ? encrypt : encrypt.substring(0, index);
  const suffix = index === -1 ? '' : encrypt.substring(index);
  const temp = decode == null ? body : format(body, 12, 3) + suffix;
  const input = temp.replace(/[^A-Za-z0-9\+\/\=]/g, '');
  const KEYS = format(key, 3, 1) + '=';
  let output = '';
  let chr1, chr2, chr3;
  let enc1, enc2, enc3, enc4;
  let i = 0;
  while (i < input.length) {
    enc1 = KEYS.indexOf(input.charAt(i++));
    enc2 = KEYS.indexOf(input.charAt(i++));
    enc3 = KEYS.indexOf(input.charAt(i++));
    enc4 = KEYS.indexOf(input.charAt(i++));
    chr1 = (enc1 << 2) | (enc2 >> 4);
    chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
    chr3 = ((enc3 & 3) << 6) | enc4;
    output = output + String.fromCharCode(chr1);
    if (enc3 != 64) {
      output = output + String.fromCharCode(chr2);
    }
    if (enc4 != 64) {
      output = output + String.fromCharCode(chr3);
    }
  }
  output = ((utftext) => {
    let string = '';
    let i = 0;
    let c = 0;
    let c2 = 0;
    let c3 = 0;
    while (i < utftext.length) {
      c = utftext.charCodeAt(i);
      if (c < 128) {
        string += String.fromCharCode(c);
        i++;
      } else if (c > 191 && c < 224) {
        c2 = utftext.charCodeAt(i + 1);
        string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
        i += 2;
      } else {
        c2 = utftext.charCodeAt(i + 1);
        c3 = utftext.charCodeAt(i + 2);
        string += String.fromCharCode(
          ((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)
        );
        i += 3;
      }
    }
    return string;
  })(output);
  return decode == null ? decodeURIComponent(output) : output;
};

// 简化的编码函数 - 使用标准 Base64 然后转换
const ENCODE_STR = (key, str, encode) => {
  const format = (encrypt, length, offset) => {
    const content = ((array, offset) => {
      const max = array.length - offset;
      if (max <= 0) {
        return array;
      }
      const result = new Array(array.length);
      for (let i = 0; i < array.length; i++) {
        if (i < offset) {
          result[i] = array[max + i];
        } else {
          result[i] = array[i - offset];
        }
      }
      return result;
    })(encrypt.split(''), offset).join('');
    const sb = [];
    let start = 0;
    while (start < content.length) {
      let end = start + length;
      if (end > content.length) {
        end = content.length;
      }
      const item = content.substring(start, end);
      sb.push(item.split('').reverse().join(''));
      start = end;
    }
    return sb.join('');
  };

  // 先用标准 Base64 编码
  const input = encode == null ? encodeURIComponent(str) : str;
  const standardBase64 = Buffer.from(input, 'utf8').toString('base64');

  // 转换为自定义编码
  const standardKeys =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
  const customKeys = format(key, 3, 1) + '=';

  let result = '';
  for (let i = 0; i < standardBase64.length; i++) {
    const char = standardBase64[i];
    const index = standardKeys.indexOf(char);
    if (index !== -1) {
      result += customKeys[index];
    } else {
      result += char;
    }
  }

  if (encode != null) {
    const index = result.indexOf('=');
    const body = index === -1 ? result : result.substring(0, index);
    const suffix = index === -1 ? '' : result.substring(index);
    return format(body, 12, 3) + suffix;
  }

  return result;
};

const KEY_ENCRYPT =
  'BAFEDIHGLKJONMRQPUTSXWVaZYdcbgfejihmlkponsrqvutyxw10z432765+98/C';

// 解码关键常量
const PRODUCT_KEY = DECODE_STR(KEY_ENCRYPT, 'RWxlQWRtaW5QbHVz='); // EleAdminPlus
const V_KEY = DECODE_STR(KEY_ENCRYPT, 'MS40='); // 1.4

console.log('Product Key:', PRODUCT_KEY);
console.log('Version Key:', V_KEY);

// 生成 license 函数
function generateLicense(options = {}) {
  const {
    version = V_KEY,
    product = PRODUCT_KEY,
    domain = null, // 不限制域名
    expiration = null // 不过期
  } = options;

  const licenseData = {
    version,
    product,
    domain,
    expiration
  };

  // 移除 null 值
  Object.keys(licenseData).forEach((key) => {
    if (licenseData[key] === null) {
      delete licenseData[key];
    }
  });

  const jsonStr = JSON.stringify(licenseData);
  console.log('License Data:', jsonStr);

  const encoded = ENCODE_STR(KEY_ENCRYPT, jsonStr, 0);
  console.log('Generated License:', encoded);

  return encoded;
}

// 验证 license 函数
function validateLicense(license) {
  try {
    const decoded = DECODE_STR(KEY_ENCRYPT, license, 0);
    console.log('Decoded string:', decoded);
    const data = JSON.parse(decoded);
    console.log('Decoded License Data:', data);
    return data;
  } catch (e) {
    console.error('Invalid license:', e);
    return null;
  }
}

// 生成不同类型的 license
console.log('\n=== 生成永久无限制 License ===');
const permanentLicense = generateLicense();

console.log('\n=== 生成带域名限制的 License ===');
const domainLicense = generateLicense({ domain: 'example.com' });

console.log('\n=== 生成带过期时间的 License ===');
const expiredLicense = generateLicense({
  expiration: Math.floor(Date.now() / 1000) + 365 * 24 * 60 * 60 // 1年后过期
});

console.log('\n=== 测试已知的常量解码 ===');
console.log(
  'PRODUCT_KEY decoded:',
  DECODE_STR(KEY_ENCRYPT, 'RWxlQWRtaW5QbHVz=')
);
console.log('V_KEY decoded:', DECODE_STR(KEY_ENCRYPT, 'MS40='));

console.log('\n=== 验证生成的 License ===');
validateLicense(permanentLicense);

// 导出函数供外部使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    generateLicense,
    validateLicense,
    DECODE_STR,
    ENCODE_STR,
    KEY_ENCRYPT
  };
}
