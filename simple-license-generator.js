// 简化的 EleAdmin Plus License 生成器
// 基于对原始代码的分析，直接提供有效的 license

const KEY_ENCRYPT = 'BAFEDIHGLKJONMRQPUTSXWVaZYdcbgfejihmlkponsrqvutyxw10z432765+98/C';

// 从原始代码复制的解码函数
const DECODE_STR = (key, encrypt, decode) => {
  const format = (encrypt, length, offset) => {
    const content = ((array, offset) => {
      const max = array.length - offset;
      if (max <= 0) {
        return array;
      }
      const result = new Array(array.length);
      for (let i = 0; i < array.length; i++) {
        if (i < offset) {
          result[i] = array[max + i];
        } else {
          result[i] = array[i - offset];
        }
      }
      return result;
    })(encrypt.split(''), offset).join('');
    const sb = [];
    let start = 0;
    while (start < content.length) {
      let end = start + length;
      if (end > content.length) {
        end = content.length;
      }
      const item = content.substring(start, end);
      sb.push(item.split('').reverse().join(''));
      start = end;
    }
    return sb.join('');
  };
  const index = encrypt.indexOf('=');
  const body = index === -1 ? encrypt : encrypt.substring(0, index);
  const suffix = index === -1 ? '' : encrypt.substring(index);
  const temp = decode == null ? body : format(body, 12, 3) + suffix;
  const input = temp.replace(/[^A-Za-z0-9\+\/\=]/g, '');
  const KEYS = format(key, 3, 1) + '=';
  let output = '';
  let chr1, chr2, chr3;
  let enc1, enc2, enc3, enc4;
  let i = 0;
  while (i < input.length) {
    enc1 = KEYS.indexOf(input.charAt(i++));
    enc2 = KEYS.indexOf(input.charAt(i++));
    enc3 = KEYS.indexOf(input.charAt(i++));
    enc4 = KEYS.indexOf(input.charAt(i++));
    chr1 = (enc1 << 2) | (enc2 >> 4);
    chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
    chr3 = ((enc3 & 3) << 6) | enc4;
    output = output + String.fromCharCode(chr1);
    if (enc3 != 64) {
      output = output + String.fromCharCode(chr2);
    }
    if (enc4 != 64) {
      output = output + String.fromCharCode(chr3);
    }
  }
  output = ((utftext) => {
    let string = '';
    let i = 0;
    let c = 0;
    let c2 = 0;
    let c3 = 0;
    while (i < utftext.length) {
      c = utftext.charCodeAt(i);
      if (c < 128) {
        string += String.fromCharCode(c);
        i++;
      } else if (c > 191 && c < 224) {
        c2 = utftext.charCodeAt(i + 1);
        string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
        i += 2;
      } else {
        c2 = utftext.charCodeAt(i + 1);
        c3 = utftext.charCodeAt(i + 2);
        string += String.fromCharCode(
          ((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)
        );
        i += 3;
      }
    }
    return string;
  })(output);
  return decode == null ? decodeURIComponent(output) : output;
};

// 预生成的有效 license（通过逆向工程获得）
const VALID_LICENSES = {
  // 永久无限制 license
  permanent: 'azJXZ2Jye9JyI04SMiojIu9WdjVHZvJHciwibkFUZsVkI6ICc1xGUulW',
  
  // 带域名限制的 license 模板（需要替换域名部分）
  domainTemplate: 'azJXZ2Jye0nII04SMiojIu9WdjVHZvJHciwibkFUZsVkI6ICIsIyc1xGUulWI6IibpFWbvRmYuUGbw1WY4Vmt92=',
  
  // 带过期时间的 license 模板
  expirationTemplate: 'azJXZ2Jye9hTI04SMiojIu9WdjVHZvJHciwibkFUZsVkI6ICIsIyc1xGUulWbpRXYylGc4VmM1YDO3EjOi42O2Uj'
};

// 验证 license 函数
function validateLicense(license) {
  try {
    const decoded = DECODE_STR(KEY_ENCRYPT, license, 0);
    console.log('Decoded string:', decoded);
    const data = JSON.parse(decoded);
    console.log('Decoded License Data:', data);
    return data;
  } catch (e) {
    console.error('Invalid license:', e);
    return null;
  }
}

// 生成 license 的简化函数
function generateLicense(type = 'permanent', options = {}) {
  switch (type) {
    case 'permanent':
      return VALID_LICENSES.permanent;
    
    case 'domain':
      // 对于域名限制，返回示例 license（实际使用时需要根据具体域名生成）
      console.log('注意：域名限制的 license 需要根据具体域名生成');
      return VALID_LICENSES.domainTemplate;
    
    case 'expiration':
      // 对于过期时间，返回示例 license（实际使用时需要根据具体时间生成）
      console.log('注意：带过期时间的 license 需要根据具体时间生成');
      return VALID_LICENSES.expirationTemplate;
    
    default:
      return VALID_LICENSES.permanent;
  }
}

// 使用说明
function printUsage() {
  console.log('='.repeat(60));
  console.log('EleAdmin Plus License 生成器');
  console.log('='.repeat(60));
  console.log('');
  console.log('1. 永久无限制 License（推荐）:');
  console.log('   License: ' + VALID_LICENSES.permanent);
  console.log('');
  console.log('2. 使用方法:');
  console.log('   在你的 Vue 应用中，使用 EleConfigProvider 组件时传入 license:');
  console.log('');
  console.log('   <template>');
  console.log('     <EleConfigProvider :license="license">');
  console.log('       <!-- 你的应用内容 -->');
  console.log('     </EleConfigProvider>');
  console.log('   </template>');
  console.log('');
  console.log('   <script>');
  console.log('   export default {');
  console.log('     data() {');
  console.log('       return {');
  console.log('         license: "' + VALID_LICENSES.permanent + '"');
  console.log('       }');
  console.log('     }');
  console.log('   }');
  console.log('   </script>');
  console.log('');
  console.log('3. 验证 License:');
  console.log('');
}

// 主程序
console.log('正在验证永久 License...');
const permanentData = validateLicense(VALID_LICENSES.permanent);

if (permanentData) {
  console.log('\n✅ License 验证成功！');
  printUsage();
} else {
  console.log('\n❌ License 验证失败！');
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    generateLicense,
    validateLicense,
    VALID_LICENSES,
    printUsage
  };
}
