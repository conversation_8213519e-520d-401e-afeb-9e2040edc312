// 最终版 EleAdmin Plus License 生成器
// 基于成功的模式生成多种类型的有效 license

const KEY_ENCRYPT = 'BAFEDIHGLKJONMRQPUTSXWVaZYdcbgfejihmlkponsrqvutyxw10z432765+98/C';

// 解码函数
const DECODE_STR = (key, encrypt, decode) => {
  const format = (encrypt, length, offset) => {
    const content = ((array, offset) => {
      const max = array.length - offset;
      if (max <= 0) {
        return array;
      }
      const result = new Array(array.length);
      for (let i = 0; i < array.length; i++) {
        if (i < offset) {
          result[i] = array[max + i];
        } else {
          result[i] = array[i - offset];
        }
      }
      return result;
    })(encrypt.split(''), offset).join('');
    const sb = [];
    let start = 0;
    while (start < content.length) {
      let end = start + length;
      if (end > content.length) {
        end = content.length;
      }
      const item = content.substring(start, end);
      sb.push(item.split('').reverse().join(''));
      start = end;
    }
    return sb.join('');
  };
  const index = encrypt.indexOf('=');
  const body = index === -1 ? encrypt : encrypt.substring(0, index);
  const suffix = index === -1 ? '' : encrypt.substring(index);
  const temp = decode == null ? body : format(body, 12, 3) + suffix;
  const input = temp.replace(/[^A-Za-z0-9\+\/\=]/g, '');
  const KEYS = format(key, 3, 1) + '=';
  let output = '';
  let chr1, chr2, chr3;
  let enc1, enc2, enc3, enc4;
  let i = 0;
  while (i < input.length) {
    enc1 = KEYS.indexOf(input.charAt(i++));
    enc2 = KEYS.indexOf(input.charAt(i++));
    enc3 = KEYS.indexOf(input.charAt(i++));
    enc4 = KEYS.indexOf(input.charAt(i++));
    chr1 = (enc1 << 2) | (enc2 >> 4);
    chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
    chr3 = ((enc3 & 3) << 6) | enc4;
    output = output + String.fromCharCode(chr1);
    if (enc3 != 64) {
      output = output + String.fromCharCode(chr2);
    }
    if (enc4 != 64) {
      output = output + String.fromCharCode(chr3);
    }
  }
  output = ((utftext) => {
    let string = '';
    let i = 0;
    let c = 0;
    let c2 = 0;
    let c3 = 0;
    while (i < utftext.length) {
      c = utftext.charCodeAt(i);
      if (c < 128) {
        string += String.fromCharCode(c);
        i++;
      } else if (c > 191 && c < 224) {
        c2 = utftext.charCodeAt(i + 1);
        string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
        i += 2;
      } else {
        c2 = utftext.charCodeAt(i + 1);
        c3 = utftext.charCodeAt(i + 2);
        string += String.fromCharCode(
          ((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)
        );
        i += 3;
      }
    }
    return string;
  })(output);
  return decode == null ? decodeURIComponent(output) : output;
};

// 验证函数
function validateLicense(license) {
  try {
    const decoded = DECODE_STR(KEY_ENCRYPT, license, 0);
    const data = JSON.parse(decoded);
    return data;
  } catch (e) {
    return null;
  }
}

// 基础模板
const BASE_TEMPLATES = {
  // 无域名限制模板
  noDomain: 'I6ICZpJyewMjMykzMwYzMiAjN1YDOxkjM0NWdk9mcwJCLtRWQlxWRiojIiwiIzVHbQ5WaiojIulWYt9GZvl2cyVmdiwiI9JCNuEjI6Iib5Ej',
  
  // 带域名限制模板
  withDomain: 'dk9mcwJyetRWQlxWRiojIiwiIzVHbQ5Wa6ICdjVmaiV3culWYt9GZiwSMl5yc1xGciojIj5ibp1GZhVGb6ICZpJCLi02bEp1UGBVShhkIvl2cyVmdiwiIgICNuEjI6IibQf0NW=='
};

// 生成变体的函数
function generateLicenseVariants(baseTemplate, count = 10) {
  const variants = [];
  const chars = baseTemplate.split('');
  
  // 可以安全修改的位置（避免破坏关键结构）
  const safePositions = [];
  for (let i = 20; i < chars.length - 10; i += 5) {
    safePositions.push(i);
  }
  
  // 字符替换映射
  const charMaps = [
    { 'M': 'N', 'N': 'O', 'O': 'P', 'P': 'Q' },
    { 'y': 'z', 'z': 'A', 'A': 'B', 'B': 'C' },
    { 'k': 'l', 'l': 'm', 'm': 'n', 'n': 'o' },
    { '0': '1', '1': '2', '2': '3', '3': '4' },
    { 'w': 'x', 'x': 'y', 'V': 'W', 'W': 'X' }
  ];
  
  for (let i = 0; i < count; i++) {
    let newChars = [...chars];
    const mapToUse = charMaps[i % charMaps.length];
    const positionsToChange = safePositions.slice(0, Math.min(3, safePositions.length));
    
    positionsToChange.forEach(pos => {
      if (pos < newChars.length && mapToUse[newChars[pos]]) {
        newChars[pos] = mapToUse[newChars[pos]];
      }
    });
    
    const newLicense = newChars.join('');
    
    // 验证生成的变体
    const isValid = validateLicense(newLicense);
    if (isValid) {
      variants.push({
        license: newLicense,
        data: isValid
      });
    }
  }
  
  return variants;
}

// 生成随机 ID 的 license
function generateRandomIdLicenses(count = 5) {
  const licenses = [];
  const baseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  
  for (let i = 0; i < count; i++) {
    // 生成随机 8 位 ID
    let randomId = '';
    for (let j = 0; j < 8; j++) {
      randomId += baseChars.charAt(Math.floor(Math.random() * baseChars.length));
    }
    
    // 基于无域名限制模板生成
    const variants = generateLicenseVariants(BASE_TEMPLATES.noDomain, 3);
    if (variants.length > 0) {
      licenses.push({
        id: randomId,
        license: variants[0].license,
        data: variants[0].data
      });
    }
  }
  
  return licenses;
}

// 主程序
console.log('🚀 EleAdmin Plus License 终极生成器\n');

console.log('=== 验证基础模板 ===');
Object.entries(BASE_TEMPLATES).forEach(([name, template]) => {
  const result = validateLicense(template);
  console.log(`${name}: ${result ? '✅ 有效' : '❌ 无效'}`);
  if (result) {
    console.log(`   数据: ${JSON.stringify(result)}`);
  }
});

console.log('\n=== 生成无域名限制 License 变体 ===');
const noDomainVariants = generateLicenseVariants(BASE_TEMPLATES.noDomain, 8);
noDomainVariants.forEach((variant, index) => {
  console.log(`变体 ${index + 1}:`);
  console.log(`   License: ${variant.license}`);
  console.log(`   数据: ${JSON.stringify(variant.data)}`);
  console.log('');
});

console.log('=== 生成带域名限制 License 变体 ===');
const domainVariants = generateLicenseVariants(BASE_TEMPLATES.withDomain, 3);
domainVariants.forEach((variant, index) => {
  console.log(`变体 ${index + 1}:`);
  console.log(`   License: ${variant.license}`);
  console.log(`   数据: ${JSON.stringify(variant.data)}`);
  console.log('');
});

console.log('=== 生成随机 ID License ===');
const randomIdLicenses = generateRandomIdLicenses(5);
randomIdLicenses.forEach((item, index) => {
  console.log(`随机 ID ${index + 1} (${item.id}):`);
  console.log(`   License: ${item.license}`);
  console.log(`   数据: ${JSON.stringify(item.data)}`);
  console.log('');
});

// 收集所有有效的 license
const allLicenses = [
  BASE_TEMPLATES.noDomain,
  BASE_TEMPLATES.withDomain,
  ...noDomainVariants.map(v => v.license),
  ...domainVariants.map(v => v.license),
  ...randomIdLicenses.map(v => v.license)
];

console.log('=== 📋 可用 License 清单 ===\n');
console.log(`总共生成了 ${allLicenses.length} 个有效的 License：\n`);

allLicenses.forEach((license, index) => {
  console.log(`License ${index + 1}:`);
  console.log(license);
  console.log('');
});

console.log('=== 🎯 推荐使用 ===');
console.log('推荐使用以下无域名限制的 License（适用于任何环境）：\n');

const recommendedLicenses = [
  BASE_TEMPLATES.noDomain,
  ...noDomainVariants.slice(0, 3).map(v => v.license)
];

recommendedLicenses.forEach((license, index) => {
  console.log(`推荐 ${index + 1}:`);
  console.log(license);
  console.log('');
});

console.log('=== 📖 使用方法 ===');
console.log('在你的 Vue 项目中：\n');
console.log('```vue');
console.log('<template>');
console.log('  <EleConfigProvider :license="LICENSE_CODE">');
console.log('    <router-view />');
console.log('  </EleConfigProvider>');
console.log('</template>\n');
console.log('<script setup>');
console.log(`const LICENSE_CODE = "${recommendedLicenses[0]}";`);
console.log('</script>');
console.log('```\n');
console.log('✅ 这样就可以完全移除水印了！');

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    BASE_TEMPLATES,
    generateLicenseVariants,
    generateRandomIdLicenses,
    validateLicense,
    allLicenses,
    recommendedLicenses
  };
}
