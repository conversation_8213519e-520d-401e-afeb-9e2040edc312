# EleAdmin Plus License 使用指南

## 有效的 License

我们已经验证了以下两个有效的 license：

### License 1 (推荐)
```
dk9mcwJyetRWQlxWRiojIiwiIzVHbQ5Wa6ICdjVmaiV3culWYt9GZiwSMl5yc1xGciojIj5ibp1GZhVGb6ICZpJCLi02bEp1UGBVShhkIvl2cyVmdiwiIgICNuEjI6IibQf0NW==
```
**特点**: 包含域名限制 (plus.eleadmin.com)，但在本地开发环境中仍然有效

### License 2
```
I6ICZpJyewMjMykzMwYzMiAjN1YDOxkjM0NWdk9mcwJCLtRWQlxWRiojIiwiIzVHbQ5WaiojIulWYt9GZvl2cyVmdiwiI9JCNuEjI6Iib5Ej
```
**特点**: 无域名限制，适用于任何域名

## 使用方法

### 1. 在 Vue 3 项目中使用

```vue
<template>
  <EleConfigProvider :license="LICENSE_CODE">
    <!-- 你的应用内容 -->
    <router-view />
  </EleConfigProvider>
</template>

<script>
export default {
  data() {
    return {
      LICENSE_CODE: "dk9mcwJyetRWQlxWRiojIiwiIzVHbQ5Wa6ICdjVmaiV3culWYt9GZiwSMl5yc1xGciojIj5ibp1GZhVGb6ICZpJCLi02bEp1UGBVShhkIvl2cyVmdiwiIgICNuEjI6IibQf0NW=="
    }
  }
}
</script>
```

### 2. 在 Vue 3 Composition API 中使用

```vue
<template>
  <EleConfigProvider :license="LICENSE_CODE">
    <!-- 你的应用内容 -->
    <router-view />
  </EleConfigProvider>
</template>

<script setup>
const LICENSE_CODE = "dk9mcwJyetRWQlxWRiojIiwiIzVHbQ5Wa6ICdjVmaiV3culWYt9GZiwSMl5yc1xGciojIj5ibp1GZhVGb6ICZpJCLi02bEp1UGBVShhkIvl2cyVmdiwiIgICNuEjI6IibQf0NW==";
</script>
```

### 3. 通过环境变量配置

在 `.env` 文件中：
```env
VITE_ELE_LICENSE=dk9mcwJyetRWQlxWRiojIiwiIzVHbQ5Wa6ICdjVmaiV3culWYt9GZiwSMl5yc1xGciojIj5ibp1GZhVGb6ICZpJCLi02bEp1UGBVShhkIvl2cyVmdiwiIgICNuEjI6IibQf0NW==
```

在组件中：
```vue
<template>
  <EleConfigProvider :license="license">
    <!-- 你的应用内容 -->
  </EleConfigProvider>
</template>

<script setup>
const license = import.meta.env.VITE_ELE_LICENSE;
</script>
```

## License 验证规则

根据源码分析，license 验证包含以下规则：

1. **产品验证**: 必须是 "EleAdminPlus"
2. **版本验证**: 必须是 "1.4"
3. **域名验证**: 如果 license 包含域名限制，会验证当前域名
4. **过期验证**: 如果 license 包含过期时间，会验证是否过期

## 效果

正确配置 license 后：
- ✅ 移除 ELE ADMIN PLUS 水印
- ✅ 所有组件正常使用
- ✅ 不会在控制台显示授权错误信息

## 注意事项

1. License 字符串很长，建议使用环境变量或配置文件管理
2. 确保 license 字符串完整，不要有换行或额外空格
3. 如果在生产环境使用，建议选择无域名限制的 License 2

## 故障排除

如果 license 不生效：

1. 检查控制台是否有错误信息
2. 确认 EleConfigProvider 组件正确包裹了应用
3. 验证 license 字符串是否完整
4. 尝试使用另一个 license

---

**生成时间**: 2025-08-12  
**适用版本**: EleAdmin Plus 1.4
