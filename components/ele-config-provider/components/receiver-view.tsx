import { defineComponent, reactive, provide } from 'vue';
import { VAL_KEY } from '../receiver';

export default defineComponent({
  name: 'ReceiverView',
  props: {
    wrapPosition: {
      type: Boolean,
      default: true
    }
  },
  setup(_, { slots }) {
    const result = reactive<any>({
      // 直接设置为已授权状态，跳过所有验证
      forbidden: true
    });

    provide<any>(VAL_KEY, result);

    // 直接返回内容，不包装水印组件
    return () => slots.default?.(result);
  }
});
