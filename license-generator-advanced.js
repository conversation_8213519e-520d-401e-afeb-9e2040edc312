// 高级 EleAdmin Plus License 生成器
// 基于已验证的有效 license 来生成新的 license

const KEY_ENCRYPT = 'BAFEDIHGLKJONMRQPUTSXWVaZYdcbgfejihmlkponsrqvutyxw10z432765+98/C';

// 从源码复制的解码函数
const DECODE_STR = (key, encrypt, decode) => {
  const format = (encrypt, length, offset) => {
    const content = ((array, offset) => {
      const max = array.length - offset;
      if (max <= 0) {
        return array;
      }
      const result = new Array(array.length);
      for (let i = 0; i < array.length; i++) {
        if (i < offset) {
          result[i] = array[max + i];
        } else {
          result[i] = array[i - offset];
        }
      }
      return result;
    })(encrypt.split(''), offset).join('');
    const sb = [];
    let start = 0;
    while (start < content.length) {
      let end = start + length;
      if (end > content.length) {
        end = content.length;
      }
      const item = content.substring(start, end);
      sb.push(item.split('').reverse().join(''));
      start = end;
    }
    return sb.join('');
  };
  const index = encrypt.indexOf('=');
  const body = index === -1 ? encrypt : encrypt.substring(0, index);
  const suffix = index === -1 ? '' : encrypt.substring(index);
  const temp = decode == null ? body : format(body, 12, 3) + suffix;
  const input = temp.replace(/[^A-Za-z0-9\+\/\=]/g, '');
  const KEYS = format(key, 3, 1) + '=';
  let output = '';
  let chr1, chr2, chr3;
  let enc1, enc2, enc3, enc4;
  let i = 0;
  while (i < input.length) {
    enc1 = KEYS.indexOf(input.charAt(i++));
    enc2 = KEYS.indexOf(input.charAt(i++));
    enc3 = KEYS.indexOf(input.charAt(i++));
    enc4 = KEYS.indexOf(input.charAt(i++));
    chr1 = (enc1 << 2) | (enc2 >> 4);
    chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
    chr3 = ((enc3 & 3) << 6) | enc4;
    output = output + String.fromCharCode(chr1);
    if (enc3 != 64) {
      output = output + String.fromCharCode(chr2);
    }
    if (enc4 != 64) {
      output = output + String.fromCharCode(chr3);
    }
  }
  output = ((utftext) => {
    let string = '';
    let i = 0;
    let c = 0;
    let c2 = 0;
    let c3 = 0;
    while (i < utftext.length) {
      c = utftext.charCodeAt(i);
      if (c < 128) {
        string += String.fromCharCode(c);
        i++;
      } else if (c > 191 && c < 224) {
        c2 = utftext.charCodeAt(i + 1);
        string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
        i += 2;
      } else {
        c2 = utftext.charCodeAt(i + 1);
        c3 = utftext.charCodeAt(i + 2);
        string += String.fromCharCode(
          ((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)
        );
        i += 3;
      }
    }
    return string;
  })(output);
  return decode == null ? decodeURIComponent(output) : output;
};

// 编码函数（DECODE_STR 的逆向实现）
const ENCODE_STR = (key, str, decode) => {
  const format = (encrypt, length, offset) => {
    const content = ((array, offset) => {
      const max = array.length - offset;
      if (max <= 0) {
        return array;
      }
      const result = new Array(array.length);
      for (let i = 0; i < array.length; i++) {
        if (i < offset) {
          result[i] = array[max + i];
        } else {
          result[i] = array[i - offset];
        }
      }
      return result;
    })(encrypt.split(''), offset).join('');
    const sb = [];
    let start = 0;
    while (start < content.length) {
      let end = start + length;
      if (end > content.length) {
        end = content.length;
      }
      const item = content.substring(start, end);
      sb.push(item.split('').reverse().join(''));
      start = end;
    }
    return sb.join('');
  };

  // UTF-8 编码
  const utfEncode = (string) => {
    let utftext = '';
    for (let n = 0; n < string.length; n++) {
      const c = string.charCodeAt(n);
      if (c < 128) {
        utftext += String.fromCharCode(c);
      } else if ((c > 127) && (c < 2048)) {
        utftext += String.fromCharCode((c >> 6) | 192);
        utftext += String.fromCharCode((c & 63) | 128);
      } else {
        utftext += String.fromCharCode((c >> 12) | 224);
        utftext += String.fromCharCode(((c >> 6) & 63) | 128);
        utftext += String.fromCharCode((c & 63) | 128);
      }
    }
    return utftext;
  };

  const input = decode == null ? encodeURIComponent(str) : str;
  const utf8Input = utfEncode(input);
  
  const KEYS = format(key, 3, 1) + '=';
  let output = '';
  let chr1, chr2, chr3;
  let enc1, enc2, enc3, enc4;
  let i = 0;

  while (i < utf8Input.length) {
    chr1 = utf8Input.charCodeAt(i++);
    chr2 = utf8Input.charCodeAt(i++);
    chr3 = utf8Input.charCodeAt(i++);

    enc1 = chr1 >> 2;
    enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
    enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
    enc4 = chr3 & 63;

    if (isNaN(chr2)) {
      enc3 = enc4 = 64;
    } else if (isNaN(chr3)) {
      enc4 = 64;
    }

    output = output +
      KEYS.charAt(enc1) + KEYS.charAt(enc2) +
      KEYS.charAt(enc3) + KEYS.charAt(enc4);
  }

  if (decode != null) {
    const index = output.indexOf('=');
    const body = index === -1 ? output : output.substring(0, index);
    const suffix = index === -1 ? '' : output.substring(index);
    return format(body, 12, 3) + suffix;
  }

  return output;
};

// 验证 license 函数
function validateLicense(license) {
  try {
    const decoded = DECODE_STR(KEY_ENCRYPT, license, 0);
    const data = JSON.parse(decoded);
    return data;
  } catch (e) {
    return null;
  }
}

// 生成随机 ID
function generateRandomId() {
  return Math.random().toString(36).substr(2, 8).toUpperCase();
}

// 生成随机数字 ID
function generateRandomNumId() {
  return Math.floor(Math.random() * 9000000000000000000) + 1000000000000000000;
}

// 生成新的 license
function generateNewLicense(options = {}) {
  const {
    id = generateRandomId(),
    product = "EleAdminPlus",
    version = "1.4",
    domain = "",
    subject = null,
    expiration = null
  } = options;

  const licenseData = {
    id: id.toString(),
    product,
    version
  };

  // 添加可选字段
  if (domain !== null) licenseData.domain = domain;
  if (subject !== null) licenseData.subject = subject;
  if (expiration !== null) licenseData.expiration = expiration;

  const jsonStr = JSON.stringify(licenseData);
  const encoded = ENCODE_STR(KEY_ENCRYPT, jsonStr, 0);
  
  return {
    license: encoded,
    data: licenseData,
    json: jsonStr
  };
}

// 生成多个不同类型的 license
console.log('=== EleAdmin Plus License 生成器 ===\n');

console.log('1. 生成无限制永久 License:');
const license1 = generateNewLicense();
console.log('   License:', license1.license);
console.log('   数据:', license1.json);
const valid1 = validateLicense(license1.license);
console.log('   验证:', valid1 ? '✅ 有效' : '❌ 无效');

console.log('\n2. 生成带数字 ID 的 License:');
const license2 = generateNewLicense({ id: generateRandomNumId() });
console.log('   License:', license2.license);
console.log('   数据:', license2.json);
const valid2 = validateLicense(license2.license);
console.log('   验证:', valid2 ? '✅ 有效' : '❌ 无效');

console.log('\n3. 生成带 subject 的 License:');
const license3 = generateNewLicense({ subject: 1 });
console.log('   License:', license3.license);
console.log('   数据:', license3.json);
const valid3 = validateLicense(license3.license);
console.log('   验证:', valid3 ? '✅ 有效' : '❌ 无效');

console.log('\n4. 生成带域名限制的 License:');
const license4 = generateNewLicense({ domain: "localhost" });
console.log('   License:', license4.license);
console.log('   数据:', license4.json);
const valid4 = validateLicense(license4.license);
console.log('   验证:', valid4 ? '✅ 有效' : '❌ 无效');

console.log('\n5. 生成带过期时间的 License (1年后过期):');
const oneYearLater = Math.floor(Date.now() / 1000) + 365 * 24 * 60 * 60;
const license5 = generateNewLicense({ expiration: oneYearLater });
console.log('   License:', license5.license);
console.log('   数据:', license5.json);
const valid5 = validateLicense(license5.license);
console.log('   验证:', valid5 ? '✅ 有效' : '❌ 无效');

// 推荐使用的 license
const validLicenses = [license1, license2, license3, license4, license5].filter((l, i) => 
  [valid1, valid2, valid3, valid4, valid5][i]
);

if (validLicenses.length > 0) {
  console.log('\n=== 推荐使用的 License ===');
  console.log('以下是生成的有效 License，任选一个使用：\n');
  
  validLicenses.forEach((license, index) => {
    console.log(`License ${index + 1}:`);
    console.log(license.license);
    console.log('');
  });
  
  console.log('使用方法：');
  console.log('<EleConfigProvider :license="LICENSE_CODE">');
  console.log('  <!-- 你的应用内容 -->');
  console.log('</EleConfigProvider>');
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    generateNewLicense,
    validateLicense,
    DECODE_STR,
    ENCODE_STR,
    KEY_ENCRYPT
  };
}
